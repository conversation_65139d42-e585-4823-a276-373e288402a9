-- 验证评价功能修复的SQL脚本
-- 执行此脚本来验证修复后的功能是否正常

-- 1. 验证task_evaluation表结构
DESCRIBE task_evaluation;

-- 2. 验证菜单权限是否正确配置
SELECT 
    m.menu_id,
    m.menu_name,
    m.parent_id,
    m.path,
    m.component,
    m.perms,
    m.menu_type,
    m.visible,
    m.status
FROM sys_menu m 
WHERE m.menu_name LIKE '%评价%' 
ORDER BY m.menu_id;

-- 3. 验证角色菜单权限分配
SELECT 
    rm.role_id,
    r.role_name,
    m.menu_name,
    m.perms 
FROM sys_role_menu rm 
JOIN sys_menu m ON rm.menu_id = m.menu_id 
JOIN sys_role r ON rm.role_id = r.role_id
WHERE m.menu_name LIKE '%评价%' 
ORDER BY rm.role_id, m.menu_id;

-- 4. 创建测试数据（如果不存在）
INSERT IGNORE INTO `app_task` (`task_id`, `task_title`, `task_desc`, `task_amount`, `task_type`, `first_type_id`, `second_type_id`, `urgent_level`, `task_status`, `publisher_id`, `publisher_name`, `publisher_avatar`, `receiver_id`, `receiver_name`, `task_address`, `longitude`, `latitude`, `start_time`, `end_time`, `view_count`, `hot_score`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
(9999, '测试评价任务', '这是一个用于测试评价功能的任务', 50.00, '0', 2, 11, '0', '2', 1000, '测试用户1', '/profile/upload/2025/08/18/WechatIMG366_20250818183852A001.jpg', 1001, '测试用户2', '北京市海淀区测试地址', '116.3', '39.9', '2025-08-24 10:00:00', '2025-08-24 12:00:00', 5, 0, 'admin', '2025-08-24 10:00:00', '', '2025-08-24 14:00:00', '测试评价任务');

-- 5. 插入多条测试评价记录来测试统计功能
INSERT IGNORE INTO `task_evaluation` (`evaluation_id`, `task_id`, `task_title`, `publisher_id`, `publisher_name`, `receiver_id`, `receiver_name`, `rating`, `evaluation_content`, `evaluation_tags`, `is_anonymous`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
(1, 9999, '测试评价任务', 1000, '测试用户1', 1001, '测试用户2', 5, '服务态度很好，任务完成得很及时，非常满意！', '["服务态度好", "及时完成", "推荐"]', '0', '0', 'app_user_1000', '2025-08-24 14:30:00', '', NULL, '测试评价记录'),
(2, 9998, '测试评价任务2', 1002, '测试用户3', 1001, '测试用户2', 4, '服务不错，但有改进空间', '["服务不错", "有改进空间"]', '0', '0', 'app_user_1002', '2025-08-24 15:00:00', '', NULL, '测试评价记录2'),
(3, 9997, '测试评价任务3', 1003, '测试用户4', 1001, '测试用户2', 3, '一般般，还可以', '["一般"]', '1', '0', 'app_user_1003', '2025-08-24 15:30:00', '', NULL, '测试评价记录3'),
(4, 9996, '测试评价任务4', 1004, '测试用户5', 1001, '测试用户2', 5, '非常棒！', '["优秀"]', '0', '0', 'app_user_1004', '2025-08-24 16:00:00', '', NULL, '测试评价记录4'),
(5, 9995, '测试评价任务5', 1005, '测试用户6', 1001, '测试用户2', 2, '不太满意', '["不满意"]', '0', '0', 'app_user_1005', '2025-08-24 16:30:00', '', NULL, '测试评价记录5');

-- 6. 测试修复后的评分统计查询（这是之前报错的查询）
SELECT rating, count(*) as count 
FROM task_evaluation 
WHERE receiver_id = 1001 AND status = '0'
GROUP BY rating
ORDER BY rating DESC;

-- 7. 测试平均评分计算
SELECT 
    receiver_id,
    receiver_name,
    COUNT(*) as total_evaluations,
    AVG(rating) as average_rating,
    ROUND(AVG(rating), 2) as average_rating_rounded
FROM task_evaluation 
WHERE receiver_id = 1001 AND status = '0'
GROUP BY receiver_id, receiver_name;

-- 8. 测试各评分等级的详细统计
SELECT 
    receiver_id,
    receiver_name,
    COUNT(*) as total_evaluations,
    AVG(rating) as average_rating,
    SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END) as five_star_count,
    SUM(CASE WHEN rating = 4 THEN 1 ELSE 0 END) as four_star_count,
    SUM(CASE WHEN rating = 3 THEN 1 ELSE 0 END) as three_star_count,
    SUM(CASE WHEN rating = 2 THEN 1 ELSE 0 END) as two_star_count,
    SUM(CASE WHEN rating = 1 THEN 1 ELSE 0 END) as one_star_count,
    CONCAT(ROUND(SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1), '%') as five_star_percentage,
    CONCAT(ROUND(SUM(CASE WHEN rating = 4 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1), '%') as four_star_percentage,
    CONCAT(ROUND(SUM(CASE WHEN rating = 3 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1), '%') as three_star_percentage,
    CONCAT(ROUND(SUM(CASE WHEN rating = 2 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1), '%') as two_star_percentage,
    CONCAT(ROUND(SUM(CASE WHEN rating = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1), '%') as one_star_percentage
FROM task_evaluation 
WHERE receiver_id = 1001 AND status = '0'
GROUP BY receiver_id, receiver_name;

-- 9. 验证评价列表查询（模拟管理后台的查询）
SELECT 
    e.evaluation_id,
    e.task_id,
    e.task_title,
    e.publisher_name,
    e.receiver_name,
    e.rating,
    e.evaluation_content,
    e.is_anonymous,
    e.status,
    e.create_time
FROM task_evaluation e
WHERE e.status = '0'
ORDER BY e.create_time DESC
LIMIT 10;

-- 10. 验证根据任务ID查询评价（模拟从任务管理页面跳转的查询）
SELECT 
    e.evaluation_id,
    e.task_id,
    e.task_title,
    e.publisher_name,
    e.receiver_name,
    e.rating,
    e.evaluation_content,
    e.is_anonymous,
    e.status,
    e.create_time
FROM task_evaluation e
WHERE e.task_id = 9999;

-- 11. 验证用户时间线事件
SELECT 
    t.timeline_id,
    t.user_id,
    t.event_type,
    t.event_title,
    t.event_desc,
    t.event_data,
    t.event_time,
    t.icon,
    t.color
FROM app_user_timeline t
WHERE t.user_id = 1001 AND t.event_type = 'task_evaluation'
ORDER BY t.event_time DESC;

-- 12. 验证检查任务是否可以评价的逻辑
SELECT 
    t.task_id,
    t.task_title,
    t.task_status,
    t.publisher_id,
    t.receiver_id,
    e.evaluation_id,
    CASE 
        WHEN t.task_status != '2' THEN '任务未完成，不能评价'
        WHEN e.evaluation_id IS NOT NULL THEN '已评价过，不能重复评价'
        ELSE '可以评价'
    END as evaluation_status
FROM app_task t
LEFT JOIN task_evaluation e ON t.task_id = e.task_id
WHERE t.task_id IN (9999, 9998, 9997, 9996, 9995)
ORDER BY t.task_id;

-- 13. 清理测试数据（可选执行）
/*
-- 取消注释以下语句来清理测试数据
DELETE FROM app_user_timeline WHERE user_id = 1001 AND event_type = 'task_evaluation';
DELETE FROM task_evaluation WHERE evaluation_id IN (1, 2, 3, 4, 5);
DELETE FROM app_task WHERE task_id IN (9999, 9998, 9997, 9996, 9995);
*/

-- 验证完成提示
SELECT '评价功能修复验证完成！' as message, NOW() as verification_time;
